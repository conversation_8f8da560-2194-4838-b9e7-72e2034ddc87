#!/bin/bash

set -e

echo "=== 启动智能评估平台 (Apple M4优化版) ==="

# 信号处理函数：优雅关闭服务
cleanup() {
    echo -e "\n${YELLOW}🛑 接收到退出信号，正在优雅关闭服务...${NC}"
    
    # 清理后端进程
    if [ -f "logs/backend-m4.pid" ]; then
        BACKEND_PID=$(cat logs/backend-m4.pid)
        if ps -p "$BACKEND_PID" > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止后端服务 (PID: $BACKEND_PID)...${NC}"
            kill -TERM "$BACKEND_PID" 2>/dev/null || kill -9 "$BACKEND_PID" 2>/dev/null
        fi
        rm -f logs/backend-m4.pid
    fi
    
    # 清理前端进程
    if [ -f "logs/uni-app-m4.pid" ]; then
        FRONTEND_PID=$(cat logs/uni-app-m4.pid)
        if ps -p "$FRONTEND_PID" > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止uni-app服务 (PID: $FRONTEND_PID)...${NC}"
            kill -TERM "$FRONTEND_PID" 2>/dev/null || kill -9 "$FRONTEND_PID" 2>/dev/null
        fi
        rm -f logs/uni-app-m4.pid
    fi
    
    if [ -f "logs/admin-m4.pid" ]; then
        ADMIN_PID=$(cat logs/admin-m4.pid)
        if ps -p "$ADMIN_PID" > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止管理后台服务 (PID: $ADMIN_PID)...${NC}"
            kill -TERM "$ADMIN_PID" 2>/dev/null || kill -9 "$ADMIN_PID" 2>/dev/null
        fi
        rm -f logs/admin-m4.pid
    fi
    
    # 清理端口占用
    echo -e "${BLUE}🔧 清理端口占用...${NC}"
    lsof -ti :8181 | xargs kill -9 2>/dev/null || true
    lsof -ti :5273 | xargs kill -9 2>/dev/null || true
    lsof -ti :5274 | xargs kill -9 2>/dev/null || true
    
    echo -e "${GREEN}✅ 服务已优雅关闭${NC}"
    exit 0
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Apple Silicon检查
check_apple_silicon() {
    echo -e "\n${BLUE}🍎 Apple Silicon环境检查${NC}"
    
    if [ "$(uname -m)" != "arm64" ]; then
        echo -e "${RED}❌ 当前不是ARM64架构，M4优化可能无效${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 检测到ARM64架构 (Apple Silicon)${NC}"
    
    # 检查内存
    TOTAL_MEM=$(sysctl hw.memsize | awk '{print $2/1024/1024/1024}')
    echo -e "${BLUE}📊 总内存: ${TOTAL_MEM}GB${NC}"
    
    if (( $(echo "$TOTAL_MEM >= 16" | bc -l) )); then
        echo -e "${GREEN}✅ 内存充足，使用高性能配置${NC}"
        export MEMORY_PROFILE="high"
    elif (( $(echo "$TOTAL_MEM >= 8" | bc -l) )); then
        echo -e "${YELLOW}⚠️ 内存中等，使用标准配置${NC}"
        export MEMORY_PROFILE="standard"
    else
        echo -e "${RED}⚠️ 内存较少，使用低内存配置${NC}"
        export MEMORY_PROFILE="low"
    fi
}

# 设置Apple Silicon优化的环境变量
setup_environment() {
    echo -e "\n${BLUE}⚙️ 设置Apple Silicon优化环境${NC}"
    
    # Java优化 (Java 17)
    case $MEMORY_PROFILE in
        "high")
            export JAVA_OPTS="-Xmx6g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m"
            export DOCKER_MEMORY="8g"
            ;;
        "standard")
            export JAVA_OPTS="-Xmx4g -Xms1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m"
            export DOCKER_MEMORY="6g"
            ;;
        "low")
            export JAVA_OPTS="-Xmx2g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=300"
            export DOCKER_MEMORY="4g"
            ;;
    esac
    
    # Node.js优化
    export NODE_OPTIONS="--max-old-space-size=4096"
    
    # Docker优化
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    echo -e "${GREEN}✅ 环境变量配置完成${NC}"
    echo -e "   JAVA_OPTS: $JAVA_OPTS"
    echo -e "   NODE_OPTIONS: $NODE_OPTIONS"
    echo -e "   DOCKER_MEMORY: $DOCKER_MEMORY"
}

# 检查并激活Conda环境
check_conda_env() {
    echo -e "\n${BLUE}🐍 Conda环境检查${NC}"
    
    if conda env list | grep -q "Assessment.*\*"; then
        echo -e "${GREEN}✅ Assessment环境已激活${NC}"
    else
        echo -e "${YELLOW}⚠️ 正在激活Assessment环境...${NC}"
        eval "$(conda shell.bash hook)"
        conda activate Assessment
        
        if conda env list | grep -q "Assessment.*\*"; then
            echo -e "${GREEN}✅ Assessment环境激活成功${NC}"
        else
            echo -e "${RED}❌ Assessment环境激活失败${NC}"
            exit 1
        fi
    fi
    
    # 检查关键工具
    echo -e "\n${BLUE}🔧 工具版本检查${NC}"
    echo -e "Java: $(java -version 2>&1 | head -n 1)"
    echo -e "Node: $(node --version)"
    echo -e "npm: $(npm --version)"
    echo -e "Docker: $(docker --version)"
}

# 启动优化的数据库服务
start_optimized_database() {
    echo -e "\n${BLUE}🗄️ 启动优化数据库服务${NC}"
    
    # 使用Apple Silicon优化的配置
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        echo -e "${YELLOW}⚠️ 数据库服务已在运行${NC}"
    else
        echo -e "${BLUE}🚀 启动PostgreSQL, Redis, MinIO (ARM64优化)...${NC}"
        
        # 设置内存限制
        export POSTGRES_SHARED_BUFFERS="256MB"
        export REDIS_MAXMEMORY="512mb"
        
        docker-compose -f docker-compose.dev.yml up -d postgres redis minio
        
        echo -e "${BLUE}⏳ 等待服务启动...${NC}"
        sleep 15
        
        # 检查服务状态
        if check_service_health "postgres" "assessment-postgres"; then
            postgres_status="✅"
        else
            postgres_status="❌"
        fi
        
        if check_service_health "redis" "assessment-redis"; then
            redis_status="✅"
        else
            redis_status="❌"
        fi
        
        if check_service_health "minio" "assessment-minio"; then
            minio_status="✅"
        else
            minio_status="❌"
        fi
        
        # 显示服务状态
        echo -e "${BLUE}📊 基础服务状态：${NC}"
        echo -e "  PostgreSQL: $postgres_status"
        echo -e "  Redis: $redis_status"
        echo -e "  MinIO: $minio_status"
    fi
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local container_name=$2
    
    # 检查容器是否在运行
    if docker ps --format 'table {{.Names}}\t{{.Status}}' | grep -q "$container_name.*Up"; then
        echo -e "${GREEN}✅ $service_name 运行正常${NC}"
        
        # 检查健康状态
        health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
        if [ "$health_status" = "healthy" ]; then
            echo -e "${GREEN}   ❤️ 健康检查通过${NC}"
        elif [ "$health_status" = "starting" ]; then
            echo -e "${YELLOW}   ⏳ 正在启动中...${NC}"
            # 等待健康检查完成
            sleep 5
            health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
            if [ "$health_status" = "healthy" ]; then
                echo -e "${GREEN}   ❤️ 健康检查通过${NC}"
            fi
        else
            echo -e "${YELLOW}   ⚠️ 健康状态: $health_status${NC}"
        fi
        return 0
    else
        echo -e "${RED}❌ $service_name 启动失败${NC}"
        echo -e "${BLUE}📋 容器日志:${NC}"
        docker logs "$container_name" --tail 10
        return 1
    fi
}

# 检查并初始化数据库
init_database() {
    echo -e "\n${BLUE}📊 检查数据库状态${NC}"
    
    # 等待PostgreSQL完全启动
    echo -e "${BLUE}⏳ 等待PostgreSQL完全启动...${NC}"
    
    max_attempts=30
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if docker exec assessment-postgres pg_isready -U assessment_user -d assessment_multitenant >/dev/null 2>&1; then
            echo -e "${GREEN}✅ PostgreSQL已就绪${NC}"
            break
        fi
        echo -e "${YELLOW}⏳ 尝试 $attempt/$max_attempts...${NC}"
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo -e "${RED}❌ PostgreSQL启动超时${NC}"
        exit 1
    fi
    
    # 检查数据库是否已初始化
    echo -e "${BLUE}🔍 检查数据库表是否存在...${NC}"
    
    # 检查关键表是否存在
    if docker exec assessment-postgres psql -U assessment_user -d assessment_multitenant -c "\dt" 2>/dev/null | grep -q "users\|organizations\|elderly"; then
        echo -e "${GREEN}✅ 数据库已初始化，跳过初始化步骤${NC}"
        
        # 显示表统计信息
        echo -e "${BLUE}📈 数据库表统计:${NC}"
        docker exec assessment-postgres psql -U assessment_user -d assessment_multitenant -c "
            SELECT 
                schemaname,
                tablename,
                CASE 
                    WHEN tablename = 'users' THEN (SELECT count(*) FROM users)
                    WHEN tablename = 'organizations' THEN (SELECT count(*) FROM organizations)
                    WHEN tablename = 'elderly' THEN (SELECT count(*) FROM elderly)
                    WHEN tablename = 'assessment_scales' THEN (SELECT count(*) FROM assessment_scales)
                    WHEN tablename = 'assessments' THEN (SELECT count(*) FROM assessments)
                    ELSE 0
                END as row_count
            FROM pg_tables 
            WHERE schemaname = 'public' 
                AND tablename IN ('users', 'organizations', 'elderly', 'assessment_scales', 'assessments')
            ORDER BY tablename;
        " 2>/dev/null | grep -v "row" | grep -v "---" | grep -v "^$" || true
        
    else
        echo -e "${YELLOW}⚠️ 数据库未初始化，开始初始化...${NC}"
        
        # 检查初始化脚本是否存在
        if [ ! -f "scripts/init-db.sql" ]; then
            echo -e "${RED}❌ 初始化脚本 scripts/init-db.sql 不存在${NC}"
            exit 1
        fi
        
        echo -e "${BLUE}🔧 正在执行数据库初始化脚本...${NC}"
        if docker exec -i assessment-postgres psql -U assessment_user -d assessment_multitenant < scripts/init-db.sql 2>/dev/null; then
            echo -e "${GREEN}✅ 数据库初始化完成${NC}"
        else
            echo -e "${YELLOW}⚠️ 初始化过程中出现一些警告（可能是重复创建），但数据库应该已正常初始化${NC}"
        fi
        
        # 再次检查是否初始化成功
        if docker exec assessment-postgres psql -U assessment_user -d assessment_multitenant -c "\dt" 2>/dev/null | grep -q "users\|organizations"; then
            echo -e "${GREEN}✅ 数据库初始化验证成功${NC}"
        else
            echo -e "${RED}❌ 数据库初始化失败${NC}"
            exit 1
        fi
    fi
}

# 启动优化的后端服务
start_optimized_backend() {
    echo -e "\n${BLUE}☕ 启动优化后端服务${NC}"
    
    cd backend
    
    # 检查并清理端口占用
    if lsof -Pi :8181 -sTCP:LISTEN -t >/dev/null; then
        echo -e "${YELLOW}⚠️ 端口8181已被占用，正在自动清理...${NC}"
        PID=$(lsof -Pi :8181 -sTCP:LISTEN -t)
        if [ -n "$PID" ]; then
            echo -e "${BLUE}🔧 杀死进程 $PID...${NC}"
            kill -9 "$PID"
            sleep 2
            echo -e "${GREEN}✅ 端口8181已释放${NC}"
        fi
    fi
    
    echo -e "${BLUE}🔨 编译并启动Spring Boot应用 (Apple Silicon优化)...${NC}"
    
    # 设置JVM参数文件
    echo "$JAVA_OPTS -Dspring.profiles.active=local" > .mvn/jvm.config
    
    # 检查Maven Wrapper是否正常
    if [ ! -f ".mvn/wrapper/maven-wrapper.jar" ] || [ $(stat -f%z ".mvn/wrapper/maven-wrapper.jar" 2>/dev/null || echo 0) -lt 10000 ]; then
        echo -e "${YELLOW}⚠️ Maven Wrapper损坏，正在修复...${NC}"
        rm -f .mvn/wrapper/maven-wrapper.jar
        echo -e "${BLUE}🔄 下载Maven Wrapper...${NC}"
        curl -s -o .mvn/wrapper/maven-wrapper.jar https://repo.maven.apache.org/maven2/io/takari/maven-wrapper/0.5.6/maven-wrapper-0.5.6.jar
        if [ -f ".mvn/wrapper/maven-wrapper.jar" ] && [ $(stat -f%z ".mvn/wrapper/maven-wrapper.jar") -gt 10000 ]; then
            echo -e "${GREEN}✅ Maven Wrapper修复成功${NC}"
        else
            echo -e "${RED}❌ Maven Wrapper修复失败${NC}"
            exit 1
        fi
    fi
    
    # 启动应用（强制重新编译确保最新代码）
    echo -e "${BLUE}🔨 强制重新编译应用以确保使用最新代码...${NC}"
    ./mvnw clean package -DskipTests --no-transfer-progress
    
    nohup java -jar target/assessment-platform-1.0.0-SNAPSHOT.jar --spring.profiles.active=local > ../logs/backend-m4.log 2>&1 &
    echo $! > ../logs/backend-m4.pid
    
    cd ..
    
    echo -e "${BLUE}⏳ 等待后端服务启动...${NC}"
    
    # 等待服务启动
    max_attempts=60
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8181/actuator/health | grep -q '"status"' >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 后端服务启动成功${NC}"
            return
        fi
        echo -e "${YELLOW}⏳ 等待启动 $attempt/$max_attempts...${NC}"
        sleep 3
        ((attempt++))
    done
    
    echo -e "${RED}❌ 后端服务启动失败，请查看日志: logs/backend-m4.log${NC}"
    tail -20 logs/backend-m4.log
}

# 启动优化的前端服务
start_optimized_frontend() {
    echo -e "\n${BLUE}🎨 启动优化前端服务${NC}"
    
    # uni-app H5
    echo -e "${BLUE}📱 启动uni-app H5开发服务器...${NC}"
    cd frontend/uni-app
    
    # 检查并清理端口占用
    if lsof -Pi :5273 -sTCP:LISTEN -t >/dev/null; then
        echo -e "${YELLOW}⚠️ 端口5273已被占用，正在自动清理...${NC}"
        PID=$(lsof -Pi :5273 -sTCP:LISTEN -t)
        if [ -n "$PID" ]; then
            echo -e "${BLUE}🔧 杀死进程 $PID...${NC}"
            kill -9 "$PID"
            sleep 2
            echo -e "${GREEN}✅ 端口5273已释放${NC}"
        fi
    fi
    
    # 设置Node.js优化参数
    export UV_USE_IO_URING=0  # 避免某些ARM64兼容性问题
    nohup npm run dev:h5 > ../../logs/uni-app-m4.log 2>&1 &
    echo $! > ../../logs/uni-app-m4.pid
    echo -e "${GREEN}✅ uni-app H5服务已启动${NC}"
    
    cd ../..
    
    # 管理后台
    echo -e "${BLUE}💼 启动管理后台开发服务器...${NC}"
    cd frontend/admin
    
    # 检查并清理端口占用
    if lsof -Pi :5274 -sTCP:LISTEN -t >/dev/null; then
        echo -e "${YELLOW}⚠️ 端口5274已被占用，正在自动清理...${NC}"
        PID=$(lsof -Pi :5274 -sTCP:LISTEN -t)
        if [ -n "$PID" ]; then
            echo -e "${BLUE}🔧 杀死进程 $PID...${NC}"
            kill -9 "$PID"
            sleep 2
            echo -e "${GREEN}✅ 端口5274已释放${NC}"
        fi
    fi
    
    nohup npm run dev > ../../logs/admin-m4.log 2>&1 &
    echo $! > ../../logs/admin-m4.pid
    echo -e "${GREEN}✅ 管理后台服务已启动${NC}"
    
    cd ../..
    
    echo -e "${BLUE}⏳ 等待前端服务完全启动...${NC}"
    sleep 8
}

# 显示性能监控信息
show_performance_info() {
    echo -e "\n${PURPLE}📊 Apple M4性能监控${NC}"
    
    # CPU信息
    echo -e "${BLUE}🔧 CPU信息:${NC}"
    echo "   $(sysctl -n machdep.cpu.brand_string)"
    echo "   核心数: $(sysctl -n hw.ncpu)"
    echo "   物理核心: $(sysctl -n hw.physicalcpu)"
    echo "   逻辑核心: $(sysctl -n hw.logicalcpu)"
    
    # 内存信息
    echo -e "\n${BLUE}💾 内存信息:${NC}"
    TOTAL_MEM=$(sysctl hw.memsize | awk '{print $2/1024/1024/1024}')
    echo "   总内存: ${TOTAL_MEM}GB"
    
    # Docker资源使用
    if command -v docker >/dev/null 2>&1; then
        echo -e "\n${BLUE}🐳 Docker资源使用:${NC}"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -5
    fi
}

# 清理浏览器缓存和token
clear_browser_cache() {
    echo -e "\n${BLUE}🧹 清理浏览器缓存和认证信息${NC}"
    
    # 清理Chrome浏览器的localStorage和sessionStorage
    if command -v osascript >/dev/null 2>&1; then
        echo -e "${BLUE}🔧 清理Chrome浏览器缓存...${NC}"
        
        # 使用AppleScript清理Chrome的开发者工具和应用缓存
        osascript << 'EOF' 2>/dev/null || true
        tell application "System Events"
            try
                tell application "Google Chrome"
                    if it is running then
                        set tabsList to every tab of every window
                        repeat with currentTab in tabsList
                            if URL of currentTab contains "localhost:5274" then
                                tell currentTab to execute javascript "
                                    try {
                                        // 清理所有存储
                                        localStorage.clear();
                                        sessionStorage.clear();
                                        
                                        // 设置开发环境清理标记
                                        sessionStorage.setItem('dev_startup_clear', 'true');
                                        
                                        console.log('缓存已清理，开发环境标记已设置');
                                        
                                        // 强制刷新页面到登录界面
                                        if (window.location.pathname !== '/login') {
                                            window.location.href = '/login';
                                        }
                                    } catch(e) {
                                        console.log('清理缓存时出错:', e);
                                    }
                                "
                            end if
                        end repeat
                    end if
                end tell
            end try
        end tell
EOF
        
        echo -e "${GREEN}✅ Chrome浏览器缓存清理完成${NC}"
    fi
    
    # 清理Safari浏览器的localStorage
    if command -v safari >/dev/null 2>&1; then
        echo -e "${BLUE}🔧 清理Safari浏览器缓存...${NC}"
        # Safari的清理可以通过删除特定的缓存文件来实现
        rm -rf ~/Library/Safari/LocalStorage/*localhost*5274* 2>/dev/null || true
        rm -rf ~/Library/Safari/Databases/*localhost*5274* 2>/dev/null || true
        echo -e "${GREEN}✅ Safari浏览器缓存清理完成${NC}"
    fi
    
    echo -e "${GREEN}✅ 浏览器缓存清理完成，确保显示登录界面${NC}"
}

# 显示访问信息
show_access_info() {
    echo -e "\n${GREEN}🎉 Apple M4优化版开发环境启动完成${NC}"
    echo
    echo -e "${PURPLE}🔗 访问地址:${NC}"
    echo "   🖥️  后端API:          http://localhost:8181"
    echo "   📚 API文档:          http://localhost:8181/swagger-ui/index.html"
    echo "   📱 前端H5:           http://localhost:5273"
    echo "   💼 管理后台:         http://localhost:5274"
    echo "   📦 MinIO控制台:      http://localhost:9001"
    echo
    echo -e "${PURPLE}🗄️ 数据库连接:${NC}"
    echo "   🐘 PostgreSQL:       localhost:5433/assessment_multitenant"
    echo "   🔴 Redis:            localhost:6380"
    echo
    echo -e "${PURPLE}🔐 默认账号:${NC}"
    echo "   👤 管理员:           admin / admin123"
    echo "   📦 MinIO:            minioadmin / minioadmin"
    echo
    echo -e "${PURPLE}📋 Apple M4优化特性:${NC}"
    echo "   ⚡ ARM64原生容器镜像"
    echo "   🚀 ZGC垃圾收集器 (Java 21)"
    echo "   💾 内存使用优化 ($MEMORY_PROFILE profile)"
    echo "   🔧 Docker BuildKit加速"
    echo "   🧹 自动清理浏览器缓存 (确保显示登录界面)"
    echo
    echo -e "${PURPLE}📄 日志文件:${NC}"
    echo "   ☕ 后端日志:         logs/backend-m4.log"
    echo "   📱 前端日志:         logs/uni-app-m4.log, logs/admin-m4.log"
    echo
    echo -e "${YELLOW}🛑 停止服务:${NC} ./scripts/dev-stop.sh 或者 Ctrl+C"
    echo -e "${BLUE}📊 性能监控:${NC} ./scripts/monitor-m4.sh"
}

# 主函数
main() {
    # 确保在项目根目录
    if [ ! -f "docker-compose.dev.yml" ]; then
        echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 执行启动流程
    check_apple_silicon
    setup_environment
    check_conda_env
    start_optimized_database
    init_database
    start_optimized_backend
    start_optimized_frontend
    
    # 等待前端服务启动后再清理缓存
    echo -e "${BLUE}⏳ 等待前端服务完全启动后清理缓存...${NC}"
    sleep 5
    clear_browser_cache
    show_performance_info
    show_access_info
    
    # 等待用户信号
    echo -e "\n${BLUE}🔄 服务正在运行中... 按 Ctrl+C 优雅关闭所有服务${NC}"
    while true; do
        sleep 1
    done
}

# 运行主函数
main "$@"