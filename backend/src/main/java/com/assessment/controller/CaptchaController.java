package com.assessment.controller;

import com.assessment.service.SimpleCaptchaService;
import com.assessment.service.CaptchaPoolService;
import com.assessment.dto.CaptchaVerifyRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 滑动验证码控制器
 * 自研滑动拼图验证码功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/api/captcha")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "验证码接口", description = "滑动验证码相关接口")
public class CaptchaController {

    private final SimpleCaptchaService captchaService;
    private final CaptchaPoolService captchaPoolService;

    /**
     * 获取验证码
     * 生成滑动拼图验证码的底图和滑块
     */
    @GetMapping("/get")
    @Operation(summary = "获取验证码", description = "生成滑动拼图验证码")
    public ResponseEntity<Map<String, Object>> getCaptcha() {
        try {
            log.info("接收到获取验证码请求");
            
            // 使用验证码池获取验证码
            var response = captchaPoolService.getCaptchaFromPool();
            log.info("验证码获取响应: success={}, message={}", response.isSuccess(), response.getMessage());
            
            // 添加防缓存头
            return ResponseEntity.ok()
                .header("Cache-Control", "no-cache, no-store, must-revalidate")
                .header("Pragma", "no-cache")
                .header("Expires", "0")
                .header("X-Content-Type-Options", "nosniff")
                .header("X-Frame-Options", "DENY")
                .body(response.getData());
        } catch (Exception e) {
            log.error("获取验证码时发生错误", e);
            throw e;
        }
    }

    /**
     * 校验验证码
     * 验证用户滑动操作是否正确
     */
    @PostMapping("/check")
    @Operation(summary = "校验验证码", description = "验证滑动拼图操作是否正确")
    public ResponseEntity<Map<String, Object>> checkCaptcha(@RequestBody CaptchaVerifyRequest request) {
        var response = captchaService.checkCaptcha(request.getToken(), request.getPointJson(), request.getVerification());
        Map<String, Object> result = new HashMap<>();
        result.put("success", response.isSuccess());
        result.put("data", response.getData());
        
        // 只有在验证失败或有具体错误消息时才返回message
        if (!response.isSuccess() || (response.getMessage() != null && !response.getMessage().isEmpty())) {
            result.put("message", response.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 二次验证
     * 用于登录时的二次验证，确保验证码有效且未被重复使用
     */
    @PostMapping("/verify")
    @Operation(summary = "二次验证", description = "登录时进行二次验证")
    public ResponseEntity<Map<String, Object>> verifyCaptcha(@RequestBody CaptchaVerifyRequest request) {
        // 对于简单实现，二次验证和校验是相同的逻辑
        var response = captchaService.checkCaptcha(request.getToken(), request.getPointJson(), request.getVerification());
        Map<String, Object> result = new HashMap<>();
        result.put("success", response.isSuccess());
        result.put("data", response.getData());
        
        // 只有在验证失败或有具体错误消息时才返回message
        if (!response.isSuccess() || (response.getMessage() != null && !response.getMessage().isEmpty())) {
            result.put("message", response.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取验证码池状态
     * 用于监控和调试验证码池的运行状态
     */
    @GetMapping("/pool/stats")
    @Operation(summary = "验证码池状态", description = "获取验证码池的统计信息")
    public ResponseEntity<Map<String, Object>> getPoolStats() {
        try {
            Map<String, Object> stats = captchaPoolService.getPoolStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取验证码池状态失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取状态失败");
            return ResponseEntity.ok(errorResponse);
        }
    }
}