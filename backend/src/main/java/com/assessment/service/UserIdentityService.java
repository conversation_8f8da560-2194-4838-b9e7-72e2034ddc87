package com.assessment.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;
import java.util.Optional;

/**
 * 用户身份识别服务
 * 负责解析用户名格式、验证机构代码等用户身份相关功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Service
@Slf4j
public class UserIdentityService {

    /**
     * 用户名格式正则表达式：支持多租户场景下的灵活用户名格式
     * 1. 传统格式：姓名拼音.工号 (如 zhangsan.001, lisi.asm)
     * 2. 机构用户名：字母数字下划线组合 (如 sh_admin, pd_assessor, rj_doctor)
     * 3. 系统用户名：superadmin, admin 等
     */
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9._-]*$");

    /**
     * 机构代码格式正则表达式
     * 省级：XX01XX (如 SH01MZ)
     * 市级：XX02XX (如 SH02MZ)  
     * 机构：XX02XX01 (如 SH02YL01)
     * 系统：SYSTEM, MAINT 等
     */
    private static final Pattern TENANT_CODE_PATTERN = Pattern.compile("^([A-Z]{2}\\d{2}[A-Z]{2}\\d{0,2}|SYSTEM|MAINT|PLATFORM|[A-Z_]+)$");

    /**
     * 解析用户名信息
     */
    public static class UserNameInfo {
        private final String namePrefix;    // 姓名拼音部分
        private final String identifier;    // 工号/角色部分
        private final String fullUsername;  // 完整用户名

        public UserNameInfo(String namePrefix, String identifier, String fullUsername) {
            this.namePrefix = namePrefix;
            this.identifier = identifier;
            this.fullUsername = fullUsername;
        }

        public String getNamePrefix() { return namePrefix; }
        public String getIdentifier() { return identifier; }
        public String getFullUsername() { return fullUsername; }
        
        /**
         * 判断是否为管理员账户
         */
        public boolean isAdminAccount() {
            return "admin".equalsIgnoreCase(identifier) || 
                   "administrator".equalsIgnoreCase(identifier);
        }
        
        /**
         * 判断是否为评估师账户
         */
        public boolean isAssessorAccount() {
            return "asm".equalsIgnoreCase(identifier) || 
                   "assessor".equalsIgnoreCase(identifier);
        }
        
        /**
         * 判断是否为督导员账户
         */
        public boolean isSupervisorAccount() {
            return "supervisor".equalsIgnoreCase(identifier) || 
                   "spv".equalsIgnoreCase(identifier);
        }
        
        @Override
        public String toString() {
            return String.format("UserNameInfo{namePrefix='%s', identifier='%s', fullUsername='%s'}", 
                                namePrefix, identifier, fullUsername);
        }
    }

    /**
     * 机构代码信息
     */
    public static class TenantCodeInfo {
        private final String code;
        private final TenantType type;
        private final int level;
        private final String region;

        public TenantCodeInfo(String code, TenantType type, int level, String region) {
            this.code = code;
            this.type = type;
            this.level = level;
            this.region = region;
        }

        public String getCode() { return code; }
        public TenantType getType() { return type; }
        public int getLevel() { return level; }
        public String getRegion() { return region; }

        public enum TenantType {
            SYSTEM,      // 系统级 (SYSTEM, platform等)
            GOVERNMENT,  // 政府机构 (XX01MZ, XX02MZ)
            NURSING_HOME,// 养老机构 (XX02YL01)
            HOSPITAL,    // 医疗机构 (XX02YY01)
            LEGACY       // 兼容旧格式 (demo_hospital等)
        }
        
        @Override
        public String toString() {
            return String.format("TenantCodeInfo{code='%s', type=%s, level=%d, region='%s'}", 
                                code, type, level, region);
        }
    }

    /**
     * 验证用户名格式是否正确
     */
    public boolean isValidUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        boolean isValid = USERNAME_PATTERN.matcher(username.trim()).matches();
        log.debug("用户名格式验证: {} -> {}", username, isValid);
        return isValid;
    }

    /**
     * 解析用户名信息
     */
    public Optional<UserNameInfo> parseUsername(String username) {
        if (!isValidUsername(username)) {
            log.warn("用户名格式不正确: {}", username);
            return Optional.empty();
        }

        String cleanUsername = username.trim();
        
        // 支持两种格式：
        // 1. 传统格式：姓名.工号 (如 zhangsan.001)
        // 2. 机构用户名：直接用户名 (如 sh_admin, pd_assessor)
        if (cleanUsername.contains(".")) {
            // 传统格式解析
            String[] parts = cleanUsername.split("\\.");
            if (parts.length != 2) {
                log.warn("用户名格式错误，应为'姓名.工号'格式: {}", username);
                return Optional.empty();
            }
            String namePrefix = parts[0].toLowerCase();
            String identifier = parts[1].toLowerCase();
            UserNameInfo info = new UserNameInfo(namePrefix, identifier, username);
            log.debug("解析传统格式用户名成功: {}", info);
            return Optional.of(info);
        } else {
            // 机构用户名格式：将整个用户名作为标识符
            String namePrefix = "user"; // 默认前缀
            String identifier = cleanUsername.toLowerCase();
            UserNameInfo info = new UserNameInfo(namePrefix, identifier, username);
            log.debug("解析机构用户名成功: {}", info);
            return Optional.of(info);
        }
    }

    /**
     * 验证机构代码格式是否正确
     */
    public boolean isValidTenantCode(String tenantCode) {
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            return false;
        }
        
        boolean isValid = TENANT_CODE_PATTERN.matcher(tenantCode.trim().toUpperCase()).matches();
        log.debug("机构代码格式验证: {} -> {}", tenantCode, isValid);
        return isValid;
    }

    /**
     * 解析机构代码信息
     */
    public Optional<TenantCodeInfo> parseTenantCode(String tenantCode) {
        if (!isValidTenantCode(tenantCode)) {
            log.warn("机构代码格式不正确: {}", tenantCode);
            return Optional.empty();
        }

        String upperCode = tenantCode.trim().toUpperCase();
        TenantCodeInfo info = analyzeTenantCode(upperCode);
        
        if (info != null) {
            log.debug("解析机构代码成功: {}", info);
            return Optional.of(info);
        } else {
            log.warn("无法解析机构代码: {}", tenantCode);
            return Optional.empty();
        }
    }

    /**
     * 分析机构代码结构
     */
    private TenantCodeInfo analyzeTenantCode(String code) {
        // 系统级代码
        if ("SYSTEM".equals(code) || "MAINT".equals(code)) {
            return new TenantCodeInfo(code, TenantCodeInfo.TenantType.SYSTEM, 0, "SYSTEM");
        }
        
        // 兼容旧格式
        if ("platform".equalsIgnoreCase(code) || code.contains("_")) {
            return new TenantCodeInfo(code, TenantCodeInfo.TenantType.LEGACY, 1, "LEGACY");
        }
        
        // 新格式代码分析 (如 SH01MZ, SH02YL01)
        if (code.length() >= 6) {
            String region = code.substring(0, 2);    // 地区码 (SH, BJ)
            String levelCode = code.substring(2, 4); // 级别码 (01=省, 02=市)
            String typeCode = code.substring(4, 6);  // 类型码 (MZ=民政, YL=养老, YY=医院)
            
            TenantCodeInfo.TenantType type = determineType(typeCode);
            int level = determineLevelFromCode(levelCode);
            
            return new TenantCodeInfo(code, type, level, region);
        }
        
        return null;
    }

    /**
     * 根据类型码确定机构类型
     */
    private TenantCodeInfo.TenantType determineType(String typeCode) {
        switch (typeCode) {
            case "MZ": return TenantCodeInfo.TenantType.GOVERNMENT;
            case "YL": return TenantCodeInfo.TenantType.NURSING_HOME;
            case "YY": return TenantCodeInfo.TenantType.HOSPITAL;
            default: return TenantCodeInfo.TenantType.LEGACY;
        }
    }

    /**
     * 根据级别码确定组织层级
     */
    private int determineLevelFromCode(String levelCode) {
        switch (levelCode) {
            case "01": return 1; // 省级
            case "02": return 2; // 市级
            case "03": return 3; // 区级
            default: return 1;
        }
    }

    /**
     * 为用户建议默认角色（基于用户名标识符）
     */
    public String suggestUserRole(UserNameInfo userInfo) {
        if (userInfo.isAdminAccount()) {
            return "ADMIN";
        } else if (userInfo.isAssessorAccount()) {
            return "ASSESSOR";
        } else if (userInfo.isSupervisorAccount()) {
            return "SUPERVISOR";
        } else {
            return "VIEWER"; // 默认角色
        }
    }

    /**
     * 验证用户名和机构代码的组合是否合理
     */
    public boolean isValidCombination(String username, String tenantCode) {
        Optional<UserNameInfo> userInfo = parseUsername(username);
        Optional<TenantCodeInfo> tenantInfo = parseTenantCode(tenantCode);
        
        if (!userInfo.isPresent() || !tenantInfo.isPresent()) {
            return false;
        }
        
        // 对于多租户系统，放宽组合限制
        // 只对真正的系统级管理员账户（如superadmin）进行严格限制
        if (("superadmin".equals(username) || "admin".equals(username)) && 
            tenantInfo.get().getType() != TenantCodeInfo.TenantType.SYSTEM &&
            tenantInfo.get().getType() != TenantCodeInfo.TenantType.LEGACY) {
            log.warn("系统管理员账户 {} 不能使用非系统级机构代码 {}", username, tenantCode);
            return false;
        }
        
        log.debug("用户名与机构代码组合验证通过: {} @ {}", username, tenantCode);
        return true;
    }
    
    /**
     * 验证邮箱格式
     */
    public boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        String emailPattern = "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$";
        boolean isValid = email.matches(emailPattern);
        log.debug("邮箱格式验证: {} -> {}", email, isValid);
        return isValid;
    }

    /**
     * 验证手机号格式
     */
    public boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        
        // 中国手机号格式：1开头，第二位是3-9，总共11位数字
        String phonePattern = "^1[3-9]\\d{9}$";
        boolean isValid = phone.matches(phonePattern);
        log.debug("手机号格式验证: {} -> {}", phone, isValid);
        return isValid;
    }

    /**
     * 检测登录标识符类型
     */
    public enum IdentifierType {
        EMAIL,        // 邮箱
        PHONE,        // 手机号
        INSTITUTIONAL // 机构用户名
    }

    /**
     * 检测标识符类型
     */
    public IdentifierType detectIdentifierType(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return IdentifierType.EMAIL; // 默认
        }
        
        identifier = identifier.trim();
        
        // 检测手机号
        if (isValidPhone(identifier)) {
            return IdentifierType.PHONE;
        }
        
        // 检测邮箱
        if (isValidEmail(identifier)) {
            return IdentifierType.EMAIL;
        }
        
        // 检测机构用户名格式
        if (USERNAME_PATTERN.matcher(identifier).matches()) {
            return IdentifierType.INSTITUTIONAL;
        }
        
        return IdentifierType.EMAIL; // 默认为邮箱类型
    }

    /**
     * 生成安全的日志信息（脱敏处理）
     */
    public String createSecureLogInfo(String username, String tenantCode) {
        if (username == null || tenantCode == null) {
            return "无效的登录信息";
        }
        
        // 用户名脱敏：显示前2个字符和后1个字符
        String maskedUsername = username.length() > 3 ? 
            username.substring(0, 2) + "***" + username.substring(username.length() - 1) : 
            "***";
            
        return String.format("用户: %s, 机构: %s", maskedUsername, tenantCode);
    }

    /**
     * 个人用户标识符脱敏处理
     */
    public String maskIndividualIdentifier(String identifier) {
        if (identifier == null || identifier.length() <= 3) {
            return "***";
        }
        
        IdentifierType type = detectIdentifierType(identifier);
        
        switch (type) {
            case PHONE:
                // 手机号脱敏：显示前3位和后2位
                return identifier.substring(0, 3) + "****" + identifier.substring(9);
                
            case EMAIL:
                // 邮箱脱敏：显示前2位和@后的域名
                if (identifier.contains("@")) {
                    String[] parts = identifier.split("@");
                    String localPart = parts[0];
                    String maskedLocal = localPart.length() > 2 ? 
                        localPart.substring(0, 2) + "***" : "***";
                    return maskedLocal + "@" + parts[1];
                }
                break;
                
            case INSTITUTIONAL:
                // 机构用户名脱敏
                return identifier.substring(0, 2) + "***";
        }
        
        // 默认脱敏方式
        return identifier.substring(0, 2) + "***" + 
               identifier.substring(identifier.length() - 1);
    }
}