package com.assessment.service;

import com.assessment.entity.multitenant.Tenant;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.dto.TenantInfoResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户缓存服务
 * 支持超大规模租户场景的高性能缓存策略
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TenantCacheService {

    private final TenantRepository tenantRepository;
    private final StringRedisTemplate stringRedisTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    // 缓存键前缀
    private static final String TENANT_CACHE_PREFIX = "tenant:";
    private static final String TENANT_SEARCH_PREFIX = "tenant:search:";
    private static final String TENANT_POPULAR_KEY = "tenant:popular";
    private static final String TENANT_STATS_KEY = "tenant:stats";

    // 缓存过期时间
    private static final Duration SEARCH_CACHE_TTL = Duration.ofMinutes(30);
    private static final Duration POPULAR_CACHE_TTL = Duration.ofHours(1);

    /**
     * 获取租户信息（带缓存）
     */
    @Cacheable(value = "tenants", key = "#tenantCode", unless = "#result == null")
    public TenantInfoResponse getTenantInfo(String tenantCode) {
        log.debug("从数据库获取租户信息: {}", tenantCode);
        
        Optional<Tenant> tenantOpt = tenantRepository.findByCodeAndStatus(
            tenantCode.toUpperCase(), Tenant.TenantStatus.ACTIVE);
        
        if (tenantOpt.isPresent()) {
            Tenant tenant = tenantOpt.get();
            return buildTenantInfoResponse(tenant);
        }
        return null;
    }

    /**
     * 获取热门租户（高频访问租户）
     */
    public List<TenantInfoResponse> getPopularTenants(int limit) {
        String cacheKey = TENANT_POPULAR_KEY + ":" + limit;
        
        try {
            String cachedData = stringRedisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                log.debug("从缓存获取热门租户");
                return objectMapper.readValue(cachedData, new TypeReference<List<TenantInfoResponse>>() {});
            }
        } catch (Exception e) {
            log.warn("读取热门租户缓存失败", e);
        }

        // 从访问统计中获取热门租户
        List<TenantInfoResponse> popularTenants = getPopularTenantsFromStats(limit);
        
        // 缓存结果
        try {
            String jsonData = objectMapper.writeValueAsString(popularTenants);
            stringRedisTemplate.opsForValue().set(cacheKey, jsonData, POPULAR_CACHE_TTL);
            log.debug("缓存热门租户数据: {} 个", popularTenants.size());
        } catch (Exception e) {
            log.warn("缓存热门租户失败", e);
        }

        return popularTenants;
    }

    /**
     * 搜索租户建议（带智能缓存）
     */
    public List<TenantInfoResponse> searchTenantSuggestions(String query, int limit) {
        if (query == null || query.trim().isEmpty()) {
            return getPopularTenants(limit);
        }

        String normalizedQuery = query.trim().toUpperCase();
        String cacheKey = TENANT_SEARCH_PREFIX + normalizedQuery + ":" + limit;
        
        try {
            String cachedData = stringRedisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                log.debug("从缓存获取搜索结果: {}", normalizedQuery);
                return objectMapper.readValue(cachedData, new TypeReference<List<TenantInfoResponse>>() {});
            }
        } catch (Exception e) {
            log.warn("读取搜索缓存失败: {}", normalizedQuery, e);
        }

        // 从数据库搜索
        log.debug("从数据库搜索租户: {}", normalizedQuery);
        Pageable pageable = PageRequest.of(0, limit);
        List<Tenant> tenants = tenantRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCaseAndStatus(
            normalizedQuery, normalizedQuery, Tenant.TenantStatus.ACTIVE, pageable).getContent();
        
        List<TenantInfoResponse> results = tenants.stream()
            .map(this::buildTenantInfoResponse)
            .collect(Collectors.toList());

        // 缓存搜索结果
        try {
            String jsonData = objectMapper.writeValueAsString(results);
            stringRedisTemplate.opsForValue().set(cacheKey, jsonData, SEARCH_CACHE_TTL);
            log.debug("缓存搜索结果: {} -> {} 个结果", normalizedQuery, results.size());
        } catch (Exception e) {
            log.warn("缓存搜索结果失败: {}", normalizedQuery, e);
        }

        // 记录搜索统计
        recordSearchStats(normalizedQuery, results.size());

        return results;
    }

    /**
     * 预热缓存（系统启动时调用）
     */
    public void warmUpCache() {
        log.info("🔥 开始预热租户缓存...");
        
        try {
            // 预热热门租户
            getPopularTenants(20);
            
            // 预热常用搜索词
            String[] commonQueries = {"PLATFORM", "SH", "HN", "HB", "HOSP", "CARE", "INS"};
            for (String query : commonQueries) {
                searchTenantSuggestions(query, 10);
            }
            
            // 预热系统级租户
            getTenantInfo("PLATFORM");
            
            log.info("✅ 租户缓存预热完成");
        } catch (Exception e) {
            log.error("❌ 租户缓存预热失败", e);
        }
    }

    /**
     * 清除特定租户缓存
     */
    @CacheEvict(value = "tenants", key = "#tenantCode")
    public void evictTenantCache(String tenantCode) {
        log.info("清除租户缓存: {}", tenantCode);
        
        // 清除相关搜索缓存
        String pattern = TENANT_SEARCH_PREFIX + "*";
        Set<String> keys = stringRedisTemplate.keys(pattern);
        if (keys != null && !keys.isEmpty()) {
            stringRedisTemplate.delete(keys);
            log.debug("清除 {} 个搜索缓存", keys.size());
        }
        
        // 清除热门租户缓存
        String popularPattern = TENANT_POPULAR_KEY + "*";
        Set<String> popularKeys = stringRedisTemplate.keys(popularPattern);
        if (popularKeys != null && !popularKeys.isEmpty()) {
            stringRedisTemplate.delete(popularKeys);
            log.debug("清除 {} 个热门租户缓存", popularKeys.size());
        }
    }

    /**
     * 清除所有租户缓存
     */
    public void evictAllTenantCache() {
        log.info("清除所有租户缓存");
        
        try {
            // 清除Spring Cache
            redisTemplate.getConnectionFactory().getConnection().serverCommands().flushDb();
            
            // 清除自定义缓存
            String[] patterns = {
                TENANT_CACHE_PREFIX + "*",
                TENANT_SEARCH_PREFIX + "*",
                TENANT_POPULAR_KEY + "*"
            };
            
            for (String pattern : patterns) {
                Set<String> keys = stringRedisTemplate.keys(pattern);
                if (keys != null && !keys.isEmpty()) {
                    stringRedisTemplate.delete(keys);
                }
            }
            
            log.info("✅ 所有租户缓存已清除");
        } catch (Exception e) {
            log.error("❌ 清除租户缓存失败", e);
        }
    }

    /**
     * 记录租户访问统计
     */
    public void recordTenantAccess(String tenantCode) {
        try {
            String key = TENANT_STATS_KEY + ":access:" + tenantCode;
            stringRedisTemplate.opsForValue().increment(key);
            stringRedisTemplate.expire(key, Duration.ofDays(7));
            
            // 记录到排行榜
            stringRedisTemplate.opsForZSet().incrementScore(
                TENANT_STATS_KEY + ":ranking", tenantCode, 1);
            
        } catch (Exception e) {
            log.warn("记录租户访问统计失败: {}", tenantCode, e);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 缓存键数量统计
            stats.put("tenantCacheCount", getKeyCount(TENANT_CACHE_PREFIX + "*"));
            stats.put("searchCacheCount", getKeyCount(TENANT_SEARCH_PREFIX + "*"));
            stats.put("popularCacheCount", getKeyCount(TENANT_POPULAR_KEY + "*"));
            
            // 热门租户排行
            Set<String> topTenants = stringRedisTemplate.opsForZSet()
                .reverseRange(TENANT_STATS_KEY + ":ranking", 0, 9);
            stats.put("topTenants", topTenants);
            
            // 缓存命中率（需要配合监控系统）
            stats.put("cacheEnabled", true);
            stats.put("lastUpdate", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.warn("获取缓存统计失败", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    // ========== 私有辅助方法 ==========

    private TenantInfoResponse buildTenantInfoResponse(Tenant tenant) {
        return TenantInfoResponse.builder()
            .code(tenant.getCode())
            .name(tenant.getName())
            .industry(tenant.getIndustry())
            .subscriptionPlan(tenant.getSubscriptionPlan().toString())
            .level(determineTenantLevel(tenant))
            .build();
    }

    private String determineTenantLevel(Tenant tenant) {
        String code = tenant.getCode();
        String industry = tenant.getIndustry();
        
        if ("PLATFORM".equals(code)) {
            return "系统级";
        }
        
        if ("政府机构".equals(industry)) {
            if (code.endsWith("_HQ")) {
                return "省级";
            } else if (code.contains("_") && code.split("_").length == 2) {
                return "市级";
            } else if (code.split("_").length > 2) {
                return "区县级";
            }
        }
        
        switch (industry) {
            case "医疗机构": return "医院";
            case "养老机构": return "养老院";
            case "保险公司": return "保险公司";
            default: return "机构级";
        }
    }

    private List<TenantInfoResponse> getPopularTenantsFromStats(int limit) {
        try {
            Set<String> popularCodes = stringRedisTemplate.opsForZSet()
                .reverseRange(TENANT_STATS_KEY + ":ranking", 0, limit - 1);
            
            if (popularCodes != null && !popularCodes.isEmpty()) {
                return popularCodes.stream()
                    .map(this::getTenantInfo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("从统计获取热门租户失败", e);
        }
        
        // 降级方案：返回系统预置的热门租户
        return getDefaultPopularTenants(limit);
    }

    private List<TenantInfoResponse> getDefaultPopularTenants(int limit) {
        String[] defaultCodes = {"PLATFORM", "SH_HQ", "HN_HQ", "HB_HQ", "HOSP_RJ", "CARE_CXM"};
        
        return Arrays.stream(defaultCodes)
            .limit(limit)
            .map(this::getTenantInfo)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private void recordSearchStats(String query, int resultCount) {
        try {
            String key = TENANT_STATS_KEY + ":search:" + query;
            stringRedisTemplate.opsForValue().increment(key);
            stringRedisTemplate.expire(key, Duration.ofDays(30));
            
            // 记录搜索结果统计
            if (resultCount > 0) {
                stringRedisTemplate.opsForZSet().incrementScore(
                    TENANT_STATS_KEY + ":search:popular", query, 1);
            }
        } catch (Exception e) {
            log.warn("记录搜索统计失败: {}", query, e);
        }
    }

    private long getKeyCount(String pattern) {
        try {
            Set<String> keys = stringRedisTemplate.keys(pattern);
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            log.warn("获取键数量失败: {}", pattern, e);
            return 0;
        }
    }
}