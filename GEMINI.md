
# Gemini 项目配置

## 项目概述

本项目是一个名为“智能评估平台”的全栈应用。它由一个基于 Spring Boot 的 Java 后端、一个基于 Vue 3 的管理后台前端，以及一个基于 uni-app 的移动端应用组成。项目还使用 Docker 进行服务编排，并包含完整的开发、测试和部署脚本。

## 技术栈

### 后端 (Java)

- **框架**: Spring Boot 3.2.4
- **语言**: Java 21
- **构建工具**: Maven
- **数据库**: PostgreSQL, Redis
- **主要依赖**:
  - `spring-boot-starter-web`: Web 应用支持
  - `spring-boot-starter-data-jpa`: 数据持久化
  - `spring-boot-starter-security`: 安全认证
  - `springdoc-openapi-starter-webmvc-ui`: API 文档 (Swagger)
  - `minio`: 对象存储
  - `jjwt`: JSON Web Tokens
  - `mapstruct`: 对象映射
  - `pdfbox`, `tika`: 文档处理
  - `testcontainers`: 集成测试

### 前端 (管理后台)

- **框架**: Vue 3.4.25
- **语言**: TypeScript
- **构建工具**: Vite
- **UI 库**: Element Plus
- **状态管理**: Pinia
- **主要依赖**:
  - `axios`: HTTP 请求
  - `echarts`: 图表
  - `tailwindcss`: CSS 框架

### 前端 (移动端/H5)

- **框架**: uni-app
- **语言**: TypeScript
- **构建工具**: Vite
- **UI 库**: uni-ui
- **状态管理**: Pinia
- **主要依赖**:
  - `@dcloudio/uni-app`: 核心框架
  - `vue`: Vue 3

### 基础设施

- **容器化**: Docker, Docker Compose
- **环境管理**: Conda

## 关键命令

### 环境设置

- **初始化开发环境**:
  ```bash
  ./scripts/setup-env.sh
  ```

### 全栈启动/停止

- **启动开发环境 (Apple Silicon)**:
  ```bash
  ./scripts/dev-start-m4.sh
  ```
- **启动开发环境 (标准)**:
  ```bash
  ./scripts/dev-start.sh
  ```
- **停止所有服务**:
  ```bash
  ./scripts/dev-stop.sh
  ```

### 后端开发

- **激活 Conda 环境**:
  ```bash
  conda activate Assessment
  ```
- **启动后端服务**:
  ```bash
  cd backend
  ./mvnw spring-boot:run
  ```
- **运行后端测试**:
  ```bash
  cd backend
  ./mvnw test
  ```
- **构建后端项目**:
  ```bash
  cd backend
  ./mvnw clean install -DskipTests
  ```

### 前端开发 (管理后台)

- **安装依赖**:
  ```bash
  cd frontend/admin
  npm install
  ```
- **启动开发服务器**:
  ```bash
  cd frontend/admin
  npm run dev
  ```
- **运行测试**:
  ```bash
  cd frontend/admin
  npm run test
  ```
- **构建项目**:
  ```bash
  cd frontend/admin
  npm run build
  ```
- **代码格式化**:
  ```bash
  cd frontend/admin
  npm run format
  ```

### 前端开发 (移动端/H5)

- **安装依赖**:
  ```bash
  cd frontend/uni-app
  npm install
  ```
- **启动 H5 开发服务器**:
  ```bash
  cd frontend/uni-app
  npm run dev:h5
  ```
- **构建 H5 应用**:
  ```bash
  cd frontend/uni-app
  npm run build:h5
  ```
- **代码格式化**:
  ```bash
  cd frontend/uni-app
  npm run format
  ```

## 目录结构

```
/
├── backend/         # 后端 Spring Boot 项目
│   ├── src/         # Java 源代码
│   └── pom.xml      # Maven 配置文件
├── frontend/        # 前端项目
│   ├── admin/       # 管理后台 (Vue 3)
│   │   ├── src/
│   │   └── package.json
│   └── uni-app/     # 移动端/H5 (uni-app)
│       ├── src/
│       └── package.json
├── scripts/         # 开发和部署脚本
├── docker-compose.yml # Docker 服务编排
├── environment.yml  # Conda 环境定义
├── QUICK_START.md   # 快速入门指南
└── GEMINI.md        # Gemini 配置文件
```
