<template>
  <div class="slide-captcha" data-env="uniapp">
    <!-- 验证码状态提示 -->
    <div class="captcha-status" v-if="verified">
      <span class="status-icon">✅</span>
      <span class="status-text">滑动验证码已通过</span>
    </div>
    <div class="captcha-status warning" v-else>
      <span class="status-icon">🔒</span>
      <span class="status-text">请完成下方滑动验证码</span>
    </div>

    <!-- 内嵌滑动验证码 -->
    <div v-if="!verified && captchaData?.originalImageBase64" class="inline-captcha">
      <!-- 背景图片容器 -->
      <div class="captcha-image-panel" :style="{ width: imgWidth + 'rpx', height: imgHeight + 'rpx' }">
        <!-- 背景图片 -->
        <img
          :src="'data:image/png;base64,' + (captchaData?.originalImageBase64 || '')"
          class="captcha-bg-image"
          draggable="false"
        />

        <!-- 滑块图片 -->
        <div
          v-if="captchaData?.jigsawImageBase64"
          class="captcha-block"
          :style="{ left: blockLeft + 'rpx', top: '0rpx', width: blockWidth + 'rpx', height: imgHeight + 'rpx' }"
        >
          <img
            :src="'data:image/png;base64,' + (captchaData?.jigsawImageBase64 || '')"
            class="captcha-block-image"
            draggable="false"
          />
        </div>

        <!-- 刷新按钮 -->
        <div class="captcha-refresh" @click="refreshCaptcha">
          <span class="refresh-icon">🔄</span>
        </div>

        <!-- 提示信息 -->
        <div v-if="tipMessage && !verifySuccess" class="captcha-tip tip-error">
          {{ tipMessage }}
        </div>
        
        <!-- 验证成功图标 -->
        <div v-if="verifySuccess" class="captcha-success">
          <span class="success-icon">✓</span>
        </div>
      </div>

      <!-- 滑动条 -->
      <div class="captcha-slider" :style="{ width: imgWidth + 'rpx', height: '80rpx' }">
        <!-- 滑动轨道 -->
        <div class="slider-track">
          <div class="slider-track-bg">
            <span class="slider-text">{{ sliderText }}</span>
          </div>

          <!-- 已滑动区域 -->
          <div class="slider-fill" :style="{ width: sliderLeft + 'rpx' }">
          </div>
        </div>

        <!-- 滑块 -->
        <div
          class="slider-button"
          :style="{ left: sliderLeft + 'rpx', width: '80rpx', height: '80rpx' }"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @mousedown="handleMouseDown"
        >
          <span class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
            {{ verifySuccess ? '✓' : '➤' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCaptcha, checkCaptcha } from '@/api/captcha'

export default {
  name: 'SlideCaptcha',

  emits: ['verified', 'error'],

  data() {
    return {
      // 验证码相关状态
      captchaToken: '',
      captchaVerification: '',
      verified: false,

      // 验证码数据
      captchaData: {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      },

      // 图片尺寸配置 (rpx单位)
      imgWidth: 620,  // 310px * 2 = 620rpx
      imgHeight: 310, // 155px * 2 = 310rpx
      blockWidth: 94, // 47px * 2 = 94rpx

      // 滑块状态
      sliderLeft: 0,
      blockLeft: 0,
      isMoving: false,
      startX: 0,
      verifySuccess: false,

      // 文本提示
      sliderText: '向右滑动完成验证',
      tipMessage: ''
    }
  },

  mounted() {
    this.initCaptcha()
  },

  methods: {
    // 初始化验证码
    async initCaptcha() {
      try {
        const response = await getCaptcha()
        if (response.success && response.data) {
          this.captchaData = response.data
          this.resetSlider()
        } else {
          this.showTip('验证码加载失败', false)
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
        this.showTip('验证码加载失败', false)
        this.$emit('error', '验证码加载失败')
      }
    },

    // 刷新验证码
    async refreshCaptcha() {
      this.resetCaptcha()
      await this.initCaptcha()
    },

    // 重置验证码状态
    resetCaptcha() {
      this.captchaData = {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      }
      this.resetSlider()
      this.tipMessage = ''
      this.verifySuccess = false
      this.verified = false
    },

    // 重置滑块状态
    resetSlider() {
      this.sliderLeft = 0
      this.blockLeft = 0
      this.isMoving = false
      this.verifySuccess = false
    },

    // 触摸开始
    handleTouchStart(e) {
      if (this.verifySuccess) return

      this.isMoving = true

      // 获取触摸起始位置，兼容不同平台
      let startX = 0
      if (e.changedTouches && e.changedTouches[0]) {
        startX = e.changedTouches[0].clientX
      } else if (e.touches && e.touches[0]) {
        startX = e.touches[0].clientX
      } else if (e.detail) {
        startX = e.detail.x
      }

      this.startX = startX
      this.tipMessage = ''
      
      // 阻止默认行为，防止页面滚动
      if (e.preventDefault) {
        e.preventDefault()
      }
    },

    // 触摸移动
    handleTouchMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      // 获取当前触摸位置，兼容不同平台
      let currentX = 0
      if (e.changedTouches && e.changedTouches[0]) {
        currentX = e.changedTouches[0].clientX
      } else if (e.touches && e.touches[0]) {
        currentX = e.touches[0].clientX
      } else if (e.detail) {
        currentX = e.detail.x
      }

      const deltaX = currentX - this.startX

      // 计算滑块位置，限制在有效范围内
      const deltaXRpx = deltaX * 2  // 将px转换为rpx
      const maxSliderDistance = this.imgWidth - 80 // 540rpx，滑块轨道范围

      // 滑块位置
      const newSliderLeft = Math.max(0, Math.min(deltaXRpx, maxSliderDistance))
      this.sliderLeft = newSliderLeft
      
      // 关键修复：拼图块位置要与后端坐标保持一致的映射关系
      // 滑块比例 -> 后端坐标 -> 前端拼图显示位置
      const sliderRatio = newSliderLeft / maxSliderDistance // [0, 1]
      
      // 后端坐标范围：[50px, 263px]
      const backendMinX = 50
      const backendMaxX = 263 // 310 - 47
      const backendRange = backendMaxX - backendMinX // 213px
      const backendX = backendMinX + sliderRatio * backendRange
      
      // 前端拼图显示坐标：后端坐标映射到前端620rpx图片容器
      // 后端310px -> 前端620rpx，所以比例是2
      const frontendImageX = (backendX / 310) * this.imgWidth // 转换到前端坐标
      this.blockLeft = Math.round(frontendImageX)
      
      // 阻止默认行为，防止页面滚动
      if (e.preventDefault) {
        e.preventDefault()
      }
    },

    // 触摸结束
    async handleTouchEnd(e) {
      if (!this.isMoving || this.verifySuccess) return

      this.isMoving = false
      await this.verifyCaptchaPosition()
    },

    // 鼠标事件处理（H5环境支持）
    handleMouseDown(e) {
      if (this.verifySuccess) return

      this.isMoving = true
      this.startX = e.clientX
      this.tipMessage = ''

      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)
      e.preventDefault()
    },

    handleMouseMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      const deltaX = e.clientX - this.startX

      // 计算滑块位置，限制在有效范围内
      const deltaXRpx = deltaX * 2  // 将px转换为rpx
      const maxSliderDistance = this.imgWidth - 80 // 540rpx，滑块轨道范围

      // 滑块位置
      const newSliderLeft = Math.max(0, Math.min(deltaXRpx, maxSliderDistance))
      this.sliderLeft = newSliderLeft
      
      // 关键修复：拼图块位置要与后端坐标保持一致的映射关系
      // 滑块比例 -> 后端坐标 -> 前端拼图显示位置
      const sliderRatio = newSliderLeft / maxSliderDistance // [0, 1]
      
      // 后端坐标范围：[50px, 263px]
      const backendMinX = 50
      const backendMaxX = 263 // 310 - 47
      const backendRange = backendMaxX - backendMinX // 213px
      const backendX = backendMinX + sliderRatio * backendRange
      
      // 前端拼图显示坐标：后端坐标映射到前端620rpx图片容器
      // 后端310px -> 前端620rpx，所以比例是2
      const frontendImageX = (backendX / 310) * this.imgWidth // 转换到前端坐标
      this.blockLeft = Math.round(frontendImageX)
    },

    async handleMouseUp() {
      if (!this.isMoving || this.verifySuccess) return

      this.isMoving = false

      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)

      await this.verifyCaptchaPosition()
    },

    // 显示提示信息
    showTip(message, isSuccess) {
      // 成功时不显示文字提示，只显示图标
      if (isSuccess) {
        this.verifySuccess = true
        this.tipMessage = ''
        return
      }
      
      this.tipMessage = message
      this.verifySuccess = false

      setTimeout(() => {
        this.tipMessage = ''
      }, 3000)
    },

    // 验证验证码位置
    async verifyCaptchaPosition() {
      try {
        // 坐标转换：前端rpx -> 后端像素坐标
        // 前端：620rpx宽度 = 310px后端图片宽度，滑块80rpx = 40px
        // 后端：310px图片宽度，拼图块47px，X范围[50px, 263px]
        const frontendSliderPos = this.sliderLeft // rpx单位
        const frontendSliderWidth = 80 // 80rpx = 40px滑块宽度
        const frontendMaxSlider = this.imgWidth - frontendSliderWidth // 540rpx可移动范围
        
        // 后端坐标范围（与后端SimpleCaptchaService.java保持一致）
        const backendMinX = 50
        const backendMaxX = 310 - 47 // 263px (CAPTCHA_WIDTH - PIECE_WIDTH)
        const backendRange = backendMaxX - backendMinX // 213px
        
        // 线性映射：前端[0, 540rpx] -> 后端[50px, 263px]
        const sliderRatio = frontendSliderPos / frontendMaxSlider
        const pixelX = Math.round(backendMinX + sliderRatio * backendRange)

        console.log('🎯 uni-app坐标转换:', {
          前端滑块位置: frontendSliderPos + 'rpx',
          滑块比例: sliderRatio.toFixed(3),
          后端X坐标: pixelX + 'px'
        })

        const verifyData = {
          captchaType: 'blockPuzzle',
          token: this.captchaData.token,
          pointJson: JSON.stringify({
            x: pixelX,
            y: 0
          }),
          verification: this.captchaData.secretKey
        }

        const response = await checkCaptcha(verifyData)

        if (response.success && response.data && response.data.result) {
          this.verifySuccess = true
          this.verified = true
          this.captchaToken = this.captchaData.token
          this.captchaVerification = this.captchaData.secretKey
          // 验证成功，不显示文字提示，只显示对勾图标
          this.tipMessage = ''

          // 发送验证成功事件
          this.$emit('verified', {
            token: this.captchaToken,
            verification: this.captchaVerification
          })
        } else {
          // 显示详细的失败信息
          const errorMsg = response.data?.message || response.message || '验证失败，请重试'
          this.showTip(errorMsg, false)
          this.resetSlider()
          
          // 验证失败后平滑刷新验证码
          setTimeout(async () => {
            try {
              // 先清除错误提示，给用户一个即将刷新的暗示
              this.tipMessage = ''
              
              // 静默刷新，不显示loading或错误提示
              const response = await getCaptcha()
              if (response.success && response.data) {
                this.captchaData = response.data
                this.resetSlider()
              }
            } catch (error) {
              console.error('静默刷新验证码失败:', error)
              // 如果静默刷新失败，才显示提示
              this.showTip('请手动刷新验证码', false)
            }
          }, 1200)  // 稍微缩短延迟，让体验更流畅
        }
      } catch (error) {
        console.error('验证失败:', error)
        this.showTip('验证异常，请重试', false)
        this.resetSlider()
        this.$emit('error', '验证异常')
        
        // 验证异常后平滑刷新验证码
        setTimeout(async () => {
          try {
            // 先清除错误提示，给用户一个即将刷新的暗示
            this.tipMessage = ''
            
            // 静默刷新，不显示loading或错误提示
            const response = await getCaptcha()
            if (response.success && response.data) {
              this.captchaData = response.data
              this.resetSlider()
            }
          } catch (error) {
            console.error('静默刷新验证码失败:', error)
            // 如果静默刷新失败，才显示提示
            this.showTip('请手动刷新验证码', false)
          }
        }, 1200)  // 稍微缩短延迟，让体验更流畅
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.slide-captcha {
  width: 100%;
  margin: 20rpx 0;
}

/* 验证码状态提示 */
.captcha-status {
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.captcha-status:not(.warning) {
  background: linear-gradient(135deg, #e8f5e8, #d4edda);
  color: #155724;
  border: 2rpx solid #c3e6cb;
}

.captcha-status.warning {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
  border: 2rpx solid #ffeaa7;
}

.status-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.status-text {
  font-weight: 500;
}

/* 内嵌验证码容器 */
.inline-captcha {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
}

/* 验证码图片面板 */
.captcha-image-panel {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  margin-bottom: 24rpx;
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 滑块拼图块 */
.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
}

/* 刷新按钮 */
.captcha-refresh {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  z-index: 3;
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8rpx);
}

.captcha-refresh:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.refresh-icon {
  font-size: 32rpx;
  color: #666;
}

/* 提示信息 */
.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
  backdrop-filter: blur(8rpx);
}

.tip-success {
  background: rgba(40, 167, 69, 0.9);
  color: white;
}

.tip-error {
  background: rgba(220, 53, 69, 0.9);
  color: white;
}

/* 验证成功图标 */
.captcha-success {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  width: 120rpx;
  height: 120rpx;
  background: rgba(40, 167, 69, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 8rpx 24rpx rgba(40, 167, 69, 0.3);
  animation: successPulse 0.6s ease-out;
}

.success-icon {
  font-size: 64rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

@keyframes successPulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* 滑动条容器 */
.captcha-slider {
  position: relative;
  margin-top: 8rpx;
}

/* 滑动轨道 */
.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 40rpx;
  overflow: hidden;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e9ecef, #f8f9fa);
  border: 2rpx solid #dee2e6;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 已滑动区域 */
.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(135deg, #28a745, #20c997);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 滑动文本 */
.slider-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #6c757d;
  user-select: none;
  pointer-events: none;
}

/* 滑块按钮 */
.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  user-select: none;
  box-shadow: 0 4rpx 16rpx rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.slider-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(0, 123, 255, 0.4);
}

.slider-button:active {
  transform: scale(0.95);
}

/* 滑块图标 */
.slider-icon {
  font-size: 32rpx;
  color: white;
  transition: all 0.3s ease;
  font-weight: bold;
}

.icon-success {
  color: #28a745;
}

.icon-normal {
  color: white;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .captcha-image-panel {
    margin-bottom: 20rpx;
  }
  
  .inline-captcha {
    padding: 20rpx;
  }
  
  .slider-text {
    font-size: 22rpx;
  }
  
  .status-text {
    font-size: 26rpx;
  }
}
</style>